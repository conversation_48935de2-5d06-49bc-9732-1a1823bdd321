import os
import eel
import subprocess

from engine.features import *
from engine.command import *

def start():
    print("Starting Jarvis...")
    eel.init("www")
    print("Eel initialized")

    try:
        playAssistantSound()
        print("Assistant sound played")
    except Exception as e:
        print(f"Error playing sound: {e}")
    
    @eel.expose
    def init():
        print("Web init called")
        try:
            subprocess.call([r'device.bat'])
            eel.hideLoader()
            speak("Ready for Face Authentication")
            print("About to start face authentication")
            
            # Skip face auth for debugging
            speak("Skipping face authentication for debugging")
            eel.hideFaceAuth()
            speak("Hello, Welcome Sir, How can i Help You")
            eel.hideStart()
            playAssistantSound()
            print("Initialization complete")
        except Exception as e:
            print(f"Error in init: {e}")
            speak("Error in initialization")
    
    @eel.expose
    def test_voice():
        print("Test voice function called")
        speak("Voice test successful")
        return "Voice test completed"
    
    print("Opening browser...")
    os.system('start msedge.exe --app="http://localhost:8000/index.html"')
    
    print("Starting Eel server...")
    eel.start('index.html', mode=None, host='localhost', block=True)

if __name__ == '__main__':
    start()
