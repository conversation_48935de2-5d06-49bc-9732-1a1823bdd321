* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

body {
  margin: 0;
  background: #282828;
  color: #eee;
  font-family: 'Helvetica Neue', <PERSON><PERSON>, 'Liberation Sans', FreeSans, sans-serif;
  font-size: 14px;
}

.glow {
  text-shadow: 0 0 0 rgba(0, 0, 0, 0);
    -webkit-transition: text-shadow 1s linear;
       -moz-transition: text-shadow 1s linear;
         -o-transition: text-shadow 1s linear;
            transition: text-shadow 1s linear;
}

.glow.in {
  text-shadow: 
    0.025em 0.025em 0.025em rgba(0, 0, 0, 0.8),
    0 0 0.5em rgba(255, 255, 255, 0.3);
}

.fade {
  opacity: 0;
    -webkit-transition: opacity 1s linear;
       -moz-transition: opacity 1s linear;
         -o-transition: opacity 1s linear;
            transition: opacity 1s linear;
}

.fade.in {
  opacity: 1;
}



